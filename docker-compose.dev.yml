services:
  # PostgreSQL Database
  postgres:
    image: postgres:15-alpine
    container_name: crawl_postgres_dev
    environment:
      POSTGRES_DB: ${POSTGRES_DB}
      POSTGRES_USER: ${POSTGRES_USER}
      POSTGRES_PASSWORD: ${POSTGRES_PASSWORD}
    ports:
      - "5432:5432"
    volumes:
      - postgres_data_dev:/var/lib/postgresql/data
      - ./scripts/init-db.sql:/docker-entrypoint-initdb.d/init-db.sql
    healthcheck:
      test: ["CMD-SHELL", "pg_isready -U ${POSTGRES_USER} -d ${POSTGRES_DB}"]
      interval: 10s
      timeout: 5s
      retries: 5
    networks:
      - crawl_network

  # RabbitMQ Message Broker
  rabbitmq:
    image: rabbitmq:3.12-management-alpine
    container_name: crawl_rabbitmq_dev
    environment:
      RABBITMQ_DEFAULT_USER: ${RABBITMQ_USER}
      RABBITMQ_DEFAULT_PASS: ${RABBITMQ_PASSWORD}
      RABBITMQ_DEFAULT_VHOST: ${RABBITMQ_VHOST}
    ports:
      - "5672:5672"
      - "15672:15672"  # Management UI
    volumes:
      - rabbitmq_data_dev:/var/lib/rabbitmq
    healthcheck:
      test: ["CMD", "rabbitmq-diagnostics", "ping"]
      interval: 10s
      timeout: 5s
      retries: 5
    networks:
      - crawl_network

  # Redis (for Celery result backend)
  redis:
    image: redis:7-alpine
    container_name: crawl_redis_dev
    ports:
      - "6379:6379"
    volumes:
      - redis_data_dev:/data
    healthcheck:
      test: ["CMD", "redis-cli", "ping"]
      interval: 10s
      timeout: 5s
      retries: 5
    networks:
      - crawl_network

  # Link Discovery Service
  link-discovery:
    build:
      context: ./services/link-discovery
      dockerfile: Dockerfile
    container_name: crawl_link_discovery_dev
    environment:
      - DEBUG=true
      - LOG_LEVEL=DEBUG
      - POSTGRES_SERVER=postgres
      - POSTGRES_USER=${POSTGRES_USER}
      - POSTGRES_PASSWORD=${POSTGRES_PASSWORD}
      - POSTGRES_DB=${POSTGRES_DB}
      - BROKER_URL=pyamqp://${RABBITMQ_USER}:${RABBITMQ_PASSWORD}@rabbitmq:5672/${RABBITMQ_VHOST}
      - RESULT_BACKEND=redis://redis:6379/0
    ports:
      - "8000:8000"
    volumes:
      - ./services/link-discovery/app:/app/app
    depends_on:
      postgres:
        condition: service_healthy
      rabbitmq:
        condition: service_healthy
      redis:
        condition: service_healthy
    healthcheck:
      test: ["CMD", "python", "-c", "import httpx; httpx.get('http://localhost:8000/health', timeout=5)"]
      interval: 30s
      timeout: 10s
      retries: 3
    networks:
      - crawl_network
    restart: unless-stopped

  # Crawl Worker Service
  crawl-worker:
    build:
      context: ./services/crawl-worker
      dockerfile: Dockerfile
    environment:
      - DEBUG=true
      - LOG_LEVEL=DEBUG
      - POSTGRES_SERVER=postgres
      - POSTGRES_USER=${POSTGRES_USER}
      - POSTGRES_PASSWORD=${POSTGRES_PASSWORD}
      - POSTGRES_DB=${POSTGRES_DB}
      - BROKER_URL=pyamqp://${RABBITMQ_USER}:${RABBITMQ_PASSWORD}@rabbitmq:5672/${RABBITMQ_VHOST}
      - RESULT_BACKEND=redis://redis:6379/0
    volumes:
      - ./services/crawl-worker/app:/app/app
    depends_on:
      postgres:
        condition: service_healthy
      rabbitmq:
        condition: service_healthy
      redis:
        condition: service_healthy
    networks:
      - crawl_network
    restart: unless-stopped
    scale: 2

  # Flower (Celery monitoring)
  flower:
    image: mher/flower:2.0
    container_name: crawl_flower_dev
    environment:
      - CELERY_BROKER_URL=pyamqp://${RABBITMQ_USER}:${RABBITMQ_PASSWORD}@rabbitmq:5672/${RABBITMQ_VHOST}
      - FLOWER_PORT=5555
    ports:
      - "5555:5555"
    depends_on:
      - rabbitmq
      - redis
    networks:
      - crawl_network

volumes:
  postgres_data_dev:
  rabbitmq_data_dev:
  redis_data_dev:

networks:
  crawl_network:
    driver: bridge
