version: '3.8'

services:
  # PostgreSQL Database
  postgres:
    image: postgres:15-alpine
    container_name: crawl_postgres_prod
    environment:
      POSTGRES_DB: ${POSTGRES_DB}
      POSTGRES_USER: ${POSTGRES_USER}
      POSTGRES_PASSWORD: ${POSTGRES_PASSWORD}
    volumes:
      - postgres_data_prod:/var/lib/postgresql/data
      - ./scripts/init-db.sql:/docker-entrypoint-initdb.d/init-db.sql
    healthcheck:
      test: ["CMD-SHELL", "pg_isready -U ${POSTGRES_USER} -d ${POSTGRES_DB}"]
      interval: 30s
      timeout: 10s
      retries: 3
    networks:
      - crawl_network
    restart: always
    deploy:
      resources:
        limits:
          memory: 1G
          cpus: '0.5'

  # RabbitMQ Message Broker
  rabbitmq:
    image: rabbitmq:3.12-management-alpine
    container_name: crawl_rabbitmq_prod
    environment:
      RABBITMQ_DEFAULT_USER: ${RABBITMQ_USER}
      RABBITMQ_DEFAULT_PASS: ${RABBITMQ_PASSWORD}
      RABBITMQ_DEFAULT_VHOST: ${RABBITMQ_VHOST}
    volumes:
      - rabbitmq_data_prod:/var/lib/rabbitmq
    healthcheck:
      test: ["CMD", "rabbitmq-diagnostics", "ping"]
      interval: 30s
      timeout: 10s
      retries: 3
    networks:
      - crawl_network
    restart: always
    deploy:
      resources:
        limits:
          memory: 512M
          cpus: '0.3'

  # Redis (for Celery result backend)
  redis:
    image: redis:7-alpine
    container_name: crawl_redis_prod
    volumes:
      - redis_data_prod:/data
    healthcheck:
      test: ["CMD", "redis-cli", "ping"]
      interval: 30s
      timeout: 10s
      retries: 3
    networks:
      - crawl_network
    restart: always
    deploy:
      resources:
        limits:
          memory: 256M
          cpus: '0.2'

  # Link Discovery Service
  link-discovery:
    build:
      context: ./services/link-discovery
      dockerfile: Dockerfile
    container_name: crawl_link_discovery_prod
    environment:
      - DEBUG=false
      - LOG_LEVEL=INFO
      - POSTGRES_SERVER=postgres
      - POSTGRES_USER=${POSTGRES_USER}
      - POSTGRES_PASSWORD=${POSTGRES_PASSWORD}
      - POSTGRES_DB=${POSTGRES_DB}
      - BROKER_URL=pyamqp://${RABBITMQ_USER}:${RABBITMQ_PASSWORD}@rabbitmq:5672/${RABBITMQ_VHOST}
      - RESULT_BACKEND=redis://redis:6379/0
    ports:
      - "8000:8000"
    depends_on:
      postgres:
        condition: service_healthy
      rabbitmq:
        condition: service_healthy
      redis:
        condition: service_healthy
    healthcheck:
      test: ["CMD", "curl", "-f", "http://localhost:8000/health"]
      interval: 30s
      timeout: 10s
      retries: 3
    networks:
      - crawl_network
    restart: always
    deploy:
      resources:
        limits:
          memory: 512M
          cpus: '0.5'
      replicas: 2

  # Crawl Worker Service
  crawl-worker:
    build:
      context: ./services/crawl-worker
      dockerfile: Dockerfile
    environment:
      - DEBUG=false
      - LOG_LEVEL=INFO
      - POSTGRES_SERVER=postgres
      - POSTGRES_USER=${POSTGRES_USER}
      - POSTGRES_PASSWORD=${POSTGRES_PASSWORD}
      - POSTGRES_DB=${POSTGRES_DB}
      - BROKER_URL=pyamqp://${RABBITMQ_USER}:${RABBITMQ_PASSWORD}@rabbitmq:5672/${RABBITMQ_VHOST}
      - RESULT_BACKEND=redis://redis:6379/0
    depends_on:
      postgres:
        condition: service_healthy
      rabbitmq:
        condition: service_healthy
      redis:
        condition: service_healthy
    networks:
      - crawl_network
    restart: always
    deploy:
      resources:
        limits:
          memory: 1G
          cpus: '0.8'
      replicas: 4

  # Nginx Load Balancer
  nginx:
    image: nginx:alpine
    container_name: crawl_nginx_prod
    ports:
      - "80:80"
      - "443:443"
    volumes:
      - ./nginx.conf:/etc/nginx/nginx.conf:ro
    depends_on:
      - link-discovery
    networks:
      - crawl_network
    restart: always

volumes:
  postgres_data_prod:
  rabbitmq_data_prod:
  redis_data_prod:

networks:
  crawl_network:
    driver: bridge
